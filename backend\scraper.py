"""
Simple Firecrawl-based job scraper for JoMaDe application.
This module handles scraping job URLs using Firecrawl API and extracting job information using OpenAI.
Includes intelligent caching using Firecrawl's session management as single source of truth.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import re
import asyncio
import signal
import json
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FuturesTimeoutError

from firecrawl import FirecrawlApp
import openai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class JobScraper:
    """Simple job scraper using Firecrawl and OpenAI with intelligent caching."""

    def __init__(self, timeout_seconds: int = 300, cache_hours: int = 24):
        """Initialize the scraper with API keys from environment.

        Args:
            timeout_seconds: Maximum time to wait for each URL scraping operation (default: 5 minutes)
            cache_hours: Hours to consider crawl results as fresh (default: 24 hours)
        """
        self.firecrawl_api_key = os.getenv('FIRECRAWL_API_KEY')
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.timeout_seconds = timeout_seconds
        self.cache_hours = cache_hours

        if not self.firecrawl_api_key:
            raise ValueError("FIRECRAWL_API_KEY not found in environment variables")
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY not found in environment variables")

        # Initialize Firecrawl
        self.firecrawl = FirecrawlApp(api_key=self.firecrawl_api_key)

        # Initialize OpenAI
        openai.api_key = self.openai_api_key
        self.openai_client = openai.OpenAI(api_key=self.openai_api_key)

        # Initialize crawl session cache
        self.crawl_cache_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'firecrawl_cache.json')
        self.crawl_cache = self._load_crawl_cache()

        logger.info(f"JobScraper initialized successfully with {timeout_seconds}s timeout and {cache_hours}h cache")

    def _load_crawl_cache(self) -> Dict[str, Any]:
        """Load crawl session cache from file."""
        try:
            if os.path.exists(self.crawl_cache_file):
                with open(self.crawl_cache_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"Failed to load crawl cache: {e}")
        return {}

    def _save_crawl_cache(self):
        """Save crawl session cache to file."""
        try:
            os.makedirs(os.path.dirname(self.crawl_cache_file), exist_ok=True)
            with open(self.crawl_cache_file, 'w') as f:
                json.dump(self.crawl_cache, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save crawl cache: {e}")

    def _get_cached_crawl_id(self, url: str) -> Optional[str]:
        """Get cached crawl ID for URL if it's still fresh."""
        url_cache = self.crawl_cache.get(url)
        if not url_cache:
            return None

        # Check if cache is still fresh
        cached_time = datetime.fromisoformat(url_cache['timestamp'])
        if datetime.now() - cached_time < timedelta(hours=self.cache_hours):
            return url_cache['crawl_id']

        return None

    def _cache_crawl_id(self, url: str, crawl_id: str):
        """Cache crawl ID for URL with current timestamp."""
        self.crawl_cache[url] = {
            'crawl_id': crawl_id,
            'timestamp': datetime.now().isoformat()
        }
        self._save_crawl_cache()

    def _get_active_crawls(self) -> List[Dict[str, Any]]:
        """Get list of active crawls from Firecrawl."""
        try:
            import requests
            headers = {"Authorization": f"Bearer {self.firecrawl_api_key}"}
            response = requests.get(f"https://api.firecrawl.dev/v1/crawl/active", headers=headers)

            if response.status_code == 200:
                data = response.json()
                return data.get('crawls', [])
            else:
                logger.warning(f"Failed to get active crawls: {response.status_code}")
                return []
        except Exception as e:
            logger.error(f"Error getting active crawls: {e}")
            return []

    def _crawl_url_with_timeout(self, url: str, source_prefix: str, log_callback=None) -> List[Dict[str, Any]]:
        """
        Internal method to crawl URL with timeout handling.
        This runs the actual crawling logic that can be interrupted.
        """
        return self._crawl_url_internal(url, source_prefix, log_callback)

    def crawl_url(self, url: str, source_prefix: str, log_callback=None, force_scrape: bool = False) -> List[Dict[str, Any]]:
        """
        Crawl a website for job listings using Firecrawl with intelligent caching.

        Args:
            url: The URL to crawl
            source_prefix: The three-letter prefix for this source (e.g., AAA)
            log_callback: Optional callback function for real-time logging
            force_scrape: If True, bypass cache and force new crawl

        Returns:
            List of job dictionaries
        """
        def log(msg_type, message):
            print(message)  # Keep console logging
            if log_callback:
                log_callback(msg_type, message)

        try:
            # Check for cached crawl results first (unless force_scrape is True)
            if not force_scrape:
                cached_crawl_id = self._get_cached_crawl_id(url)
                if cached_crawl_id:
                    log("info", f"🔄 CACHE HIT: Found recent crawl for {url} (ID: {cached_crawl_id[:8]}...)")
                    logger.info(f"Using cached crawl {cached_crawl_id} for URL: {url}")

                    try:
                        # Try to get the cached crawl results
                        cached_results = self._get_crawl_results(cached_crawl_id, url, source_prefix, log_callback)
                        if cached_results:
                            log("success", f"✅ CACHE SUCCESS: Retrieved {len(cached_results)} jobs from cached crawl")
                            return cached_results
                        else:
                            log("warning", f"⚠️ CACHE MISS: Cached crawl {cached_crawl_id[:8]}... no longer available")
                    except Exception as e:
                        log("warning", f"⚠️ CACHE ERROR: Failed to retrieve cached results: {str(e)}")
                        logger.warning(f"Failed to retrieve cached crawl {cached_crawl_id}: {e}")

            log("info", f"🕷️ NEW CRAWL: {url} (prefix: {source_prefix}) - Timeout: {self.timeout_seconds}s")
            logger.info(f"Starting new crawl for URL: {url} with prefix: {source_prefix}, timeout: {self.timeout_seconds}s")

            # Use ThreadPoolExecutor with timeout to prevent hanging
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(self._crawl_url_with_timeout, url, source_prefix, log_callback)
                try:
                    result = future.result(timeout=self.timeout_seconds)
                    return result
                except FuturesTimeoutError:
                    log("error", f"⏰ TIMEOUT: Crawling {url} exceeded {self.timeout_seconds} seconds")
                    logger.warning(f"Crawling {url} timed out after {self.timeout_seconds} seconds")
                    return []
                except Exception as e:
                    log("error", f"❌ ERROR in timeout wrapper for {url}: {str(e)}")
                    logger.error(f"Error in timeout wrapper for {url}: {str(e)}")
                    return []

        except Exception as e:
            log("error", f"❌ ERROR setting up crawl for {url}: {str(e)}")
            logger.error(f"Error setting up crawl for {url}: {str(e)}")
            return []

    def _get_crawl_results(self, crawl_id: str, url: str, source_prefix: str, log_callback=None) -> List[Dict[str, Any]]:
        """Retrieve results from a cached Firecrawl crawl session."""
        def log(msg_type, message):
            print(message)  # Keep console logging
            if log_callback:
                log_callback(msg_type, message)

        try:
            log("info", f"📥 RETRIEVING: Cached crawl results for {url}")

            # Use Firecrawl's check_crawl_status method to get results
            crawl_status = self.firecrawl.check_crawl_status(crawl_id)

            if not crawl_status or crawl_status.status != 'completed':
                log("warning", f"⚠️ Cached crawl {crawl_id[:8]}... is not completed (status: {crawl_status.status if crawl_status else 'None'})")
                return []

            # Process the cached crawl data
            pages_data = crawl_status.data if crawl_status.data else []

            if not pages_data:
                log("warning", f"⚠️ No data found in cached crawl {crawl_id[:8]}...")
                return []

            log("info", f"📄 Processing {len(pages_data)} cached pages from crawl {crawl_id[:8]}...")

            # Process all cached pages to extract jobs (reuse existing logic)
            all_jobs = []
            for i, page in enumerate(pages_data):
                # Handle different page data structures from Firecrawl SDK
                page_markdown = None
                page_url = url

                # Current Firecrawl SDK returns dict with 'markdown' and 'metadata' keys
                if isinstance(page, dict):
                    page_markdown = page.get('markdown')
                    if page.get('metadata') and isinstance(page['metadata'], dict):
                        page_url = page['metadata'].get('sourceURL', url)
                # Fallback for object-based responses
                elif hasattr(page, 'markdown'):
                    page_markdown = page.markdown
                    if hasattr(page, 'metadata'):
                        if hasattr(page.metadata, 'sourceURL'):
                            page_url = page.metadata.sourceURL
                        elif isinstance(page.metadata, dict):
                            page_url = page.metadata.get('sourceURL', url)

                if not page_markdown:
                    log("warning", f"⚠️ No markdown content found for cached page {i+1}")
                    continue

                log("info", f"🔍 Extracting jobs from cached page {i+1}/{len(pages_data)}: {page_url}")

                # Extract jobs from this page using existing logic
                page_jobs = self._extract_jobs_from_markdown(page_markdown, page_url, source_prefix, log_callback)
                all_jobs.extend(page_jobs)

            log("success", f"✅ CACHED EXTRACTION: Found {len(all_jobs)} jobs from cached crawl")
            return all_jobs

        except Exception as e:
            log("error", f"❌ ERROR retrieving cached crawl {crawl_id[:8]}...: {str(e)}")
            logger.error(f"Error retrieving cached crawl {crawl_id}: {str(e)}")
            return []

    def _crawl_url_internal(self, url: str, source_prefix: str, log_callback=None) -> List[Dict[str, Any]]:
        """
        Internal crawling method that performs the actual Firecrawl API calls.

        Args:
            url: The URL to crawl
            source_prefix: The three-letter prefix for this source (e.g., AAA)
            log_callback: Optional callback function for real-time logging

        Returns:
            List of job dictionaries
        """
        def log(msg_type, message):
            print(message)  # Keep console logging
            if log_callback:
                log_callback(msg_type, message)

        try:
            log("info", f"📡 Calling Firecrawl CRAWL API for {url}...")
            from firecrawl import ScrapeOptions

            # Use async crawl to get crawl ID for caching, then wait for completion
            log("info", f"🚀 Starting async crawl for {url}...")
            async_crawl_result = self.firecrawl.async_crawl_url(
                url,
                limit=10,  # Reduced from 50 to 10 pages to prevent timeouts
                scrape_options=ScrapeOptions(
                    formats=['markdown'],
                    only_main_content=True
                )
            )

            if not async_crawl_result or not async_crawl_result.success:
                log("error", f"❌ Failed to start crawl for {url}")
                logger.warning(f"Failed to start crawl for {url}")
                return []

            crawl_id = async_crawl_result.id
            if not crawl_id:
                log("error", f"❌ No crawl ID returned for {url}")
                logger.warning(f"No crawl ID returned for {url}")
                return []

            # Cache the crawl ID for future use
            self._cache_crawl_id(url, crawl_id)
            log("info", f"💾 Cached crawl ID {crawl_id[:8]}... for {url}")

            # Wait for crawl completion and get results
            log("info", f"⏳ Waiting for crawl {crawl_id[:8]}... to complete...")
            crawl_result = self.firecrawl.check_crawl_status(crawl_id)

            # Handle crawl status response
            if not crawl_result or crawl_result.status != 'completed':
                log("error", f"❌ Crawl {crawl_id[:8]}... failed or not completed (status: {crawl_result.status if crawl_result else 'None'})")
                logger.warning(f"Crawl {crawl_id} failed or not completed for {url}")
                return []

            # Get the crawled pages data
            pages_data = crawl_result.data if crawl_result.data else []

            if not pages_data:
                log("error", f"❌ No content crawled from {url}")
                logger.warning(f"No content crawled from {url}")
                return []
            log("success", f"✅ Crawled {len(pages_data)} pages from {url}")
            logger.info(f"Crawled {len(pages_data)} pages from {url}")

            # Process all crawled pages to extract jobs
            all_jobs = []
            for i, page in enumerate(pages_data):
                # Handle different page data structures from Firecrawl SDK
                page_markdown = None
                page_url = url

                # Current Firecrawl SDK returns dict with 'markdown' and 'metadata' keys
                if isinstance(page, dict):
                    page_markdown = page.get('markdown')
                    if page.get('metadata') and isinstance(page['metadata'], dict):
                        page_url = page['metadata'].get('sourceURL', url)
                # Fallback for object-based responses
                elif hasattr(page, 'markdown'):
                    page_markdown = page.markdown
                    if hasattr(page, 'metadata'):
                        if hasattr(page.metadata, 'sourceURL'):
                            page_url = page.metadata.sourceURL
                        elif isinstance(page.metadata, dict):
                            page_url = page.metadata.get('sourceURL', url)

                if page_markdown:
                    log("info", f"🔍 Processing page {i+1}/{len(pages_data)}: {page_url}")

                    # Extract job information using OpenAI
                    jobs = self._extract_jobs_with_llm(
                        page_markdown,
                        page_url,
                        source_prefix,
                        page_number=i+1,
                        log_callback=log_callback
                    )

                    if jobs:
                        all_jobs.extend(jobs)
                        log("success", f"  ✅ Found {len(jobs)} jobs on this page")
                else:
                    log("warning", f"⚠️  Page {i+1} has no markdown content, skipping")
                    # Debug: show what we actually got
                    if isinstance(page, dict):
                        log("warning", f"     Available keys: {list(page.keys())}")
                    else:
                        log("warning", f"     Page type: {type(page)}, attributes: {[attr for attr in dir(page) if not attr.startswith('_')]}")

            log("success", f"🎯 TOTAL: Extracted {len(all_jobs)} jobs from {url}")
            logger.info(f"Successfully extracted {len(all_jobs)} jobs from {url}")
            return all_jobs

        except Exception as e:
            log("error", f"❌ ERROR crawling {url}: {str(e)}")
            logger.error(f"Error crawling {url}: {str(e)}")
            return []

    def _extract_jobs_with_llm(self, content: str, source_url: str, source_prefix: str, page_number: int = 1, log_callback=None) -> List[Dict[str, Any]]:
        """
        Extract job information from scraped content using OpenAI.

        Args:
            content: The scraped markdown content
            source_url: The original URL
            source_prefix: The three-letter prefix for this source
            page_number: The page number being processed
            log_callback: Optional callback function for real-time logging

        Returns:
            List of job dictionaries
        """
        def log(msg_type, message):
            print(message)  # Keep console logging
            if log_callback:
                log_callback(msg_type, message)
        try:
            # Generic job extraction prompt - works with any website structure
            prompt = f"""
            Extract ALL job postings from this content. Look for ANY pattern that indicates a job listing:
            - Job titles, position names, role names
            - Company names or hiring organizations
            - Locations, cities, countries, "remote"
            - Links to job details or applications
            - Any text that looks like a job posting

            IMPORTANT: Different websites structure job data differently. Be flexible and extract jobs even if the format is unusual.

            For each job found, return:
            {{
                "title": "exact job title found",
                "company": "company name if found, otherwise 'Not specified'",
                "location": "location if found, otherwise 'Not specified'",
                "summary": "brief summary or first sentence of job description (max 150 chars)",
                "detail_url": "direct job URL if found, otherwise '{source_url}'"
            }}

            Return ONLY a valid JSON array. Extract even partial information - better to have incomplete jobs than miss them.

            Content:
            {content[:8000]}
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",  # Better model for extraction
                messages=[
                    {"role": "system", "content": "You are a job listing extraction expert. You MUST return valid JSON array format. Extract ALL job postings from the content, even if information is incomplete."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=4000,  # More tokens for better extraction
                temperature=0.0   # Deterministic output
            )

            # Parse the response
            response_text = response.choices[0].message.content.strip()
            log("info", f"🤖 LLM Response length: {len(response_text)} characters")

            # Try to extract JSON from the response
            import json
            import re

            jobs_data = []
            try:
                # First try: direct JSON parsing
                jobs_data = json.loads(response_text)
                log("success", f"✅ Direct JSON parsing successful: {len(jobs_data)} jobs")
            except json.JSONDecodeError:
                try:
                    # Second try: extract JSON array from response
                    json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
                    if json_match:
                        jobs_data = json.loads(json_match.group())
                        log("success", f"✅ Regex JSON extraction successful: {len(jobs_data)} jobs")
                    else:
                        log("error", "❌ No JSON array found in LLM response")
                        log("error", f"Response preview: {response_text[:500]}...")
                        return []
                except json.JSONDecodeError as e:
                    log("error", f"❌ JSON parsing failed: {str(e)}")
                    log("error", f"Response preview: {response_text[:500]}...")
                    return []

            # Process and format the jobs
            jobs = []
            for i, job_data in enumerate(jobs_data[:20]):  # Limit to 20 jobs per page
                job_id = f"{source_prefix}{page_number:02d}{i+1:02d}"  # e.g., AAA0101, AAA0102

                job = {
                    "id": job_id,
                    "title": job_data.get("title", "Unknown Position"),
                    "company": job_data.get("company", "Not specified"),
                    "location": job_data.get("location", "Not specified"),
                    "description": job_data.get("summary", job_data.get("description", "No description available"))[:200],
                    "source": source_prefix,
                    "link": job_data.get("detail_url", job_data.get("link", source_url)),
                    "isShortlisted": False,
                    "scraped_at": datetime.now().isoformat()
                }
                jobs.append(job)

            return jobs

        except Exception as e:
            logger.error(f"Error extracting jobs with LLM: {str(e)}")
            return []

    def scrape_multiple_urls(self, urls_with_prefixes: List[Dict[str, str]], log_callback=None, force_scrape: bool = False) -> Dict[str, Any]:
        """
        Scrape multiple URLs for job listings with intelligent caching.

        Args:
            urls_with_prefixes: List of dicts with 'url' and 'prefix' keys
            log_callback: Optional callback function for real-time logging
            force_scrape: If True, bypass cache and force new crawls

        Returns:
            Dictionary with scraping results
        """
        def log(msg_type, message):
            print(message)  # Keep console logging
            if log_callback:
                log_callback(msg_type, message)

        log("info", f"🚀 STARTING BATCH SCRAPING: {len(urls_with_prefixes)} URLs")
        logger.info(f"Starting batch scraping of {len(urls_with_prefixes)} URLs")

        all_jobs = []
        successful_urls = 0
        failed_urls = []
        successful_prefixes = []

        for i, url_data in enumerate(urls_with_prefixes, 1):
            url = url_data.get('url', '')
            prefix = url_data.get('prefix', 'AAA')

            if not url:
                log("warning", f"⚠️  Skipping empty URL at position {i}")
                continue

            log("info", f"📋 Processing {i}/{len(urls_with_prefixes)}: {prefix}")
            log("info", f"🔗 URL: {url}")

            # Add progress information to the log callback
            if log_callback:
                log_callback("progress", f"Processing {i}/{len(urls_with_prefixes)}: {prefix}", {
                    "current_index": i,
                    "total_urls": len(urls_with_prefixes),
                    "current_prefix": prefix,
                    "current_url": url,
                    "completed_urls": i - 1,
                    "successful_urls": successful_urls,
                    "failed_urls": len(failed_urls)
                })

            jobs = self.crawl_url(url, prefix, log_callback, force_scrape)
            if jobs:
                all_jobs.extend(jobs)
                successful_urls += 1
                successful_prefixes.append(prefix)
                log("success", f"✅ Success: {len(jobs)} jobs added (Total: {len(all_jobs)})")
                if log_callback:
                    log_callback("success", f"✅ Success: {len(jobs)} jobs added (Total: {len(all_jobs)})", {
                        "jobs_found": len(jobs),
                        "total_jobs": len(all_jobs),
                        "successful_urls": successful_urls,
                        "current_prefix": prefix
                    })
            else:
                failed_urls.append(url)
                log("error", f"❌ Failed: No jobs found")
                if log_callback:
                    log_callback("error", f"❌ Failed: No jobs found", {
                        "failed_urls": len(failed_urls) + 1,
                        "current_prefix": prefix
                    })

        log("info", f"🎯 SCRAPING COMPLETE:")
        log("info", f"   📊 Total Jobs: {len(all_jobs)}")
        log("info", f"   ✅ Successful URLs: {successful_urls}/{len(urls_with_prefixes)}")
        if failed_urls:
            log("warning", f"   ❌ Failed URLs: {len(failed_urls)}")

        result = {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "url_count": len(urls_with_prefixes),
            "successful_urls": successful_urls,
            "successful_prefixes": successful_prefixes,
            "failed_urls": failed_urls,
            "job_count": len(all_jobs),
            "jobs": all_jobs,
            "message": f"Scraped {len(all_jobs)} jobs from {successful_urls}/{len(urls_with_prefixes)} URLs"
        }

        logger.info(f"Batch scraping completed: {len(all_jobs)} jobs from {successful_urls}/{len(urls_with_prefixes)} URLs")
        return result


def create_scraper(timeout_seconds: int = 180, cache_hours: int = 24) -> Optional[JobScraper]:
    """
    Factory function to create a JobScraper instance with intelligent caching.
    Returns None if API keys are not configured.

    Args:
        timeout_seconds: Maximum time to wait for each URL scraping operation (default: 3 minutes)
        cache_hours: Hours to consider crawl results as fresh (default: 24 hours)
    """
    try:
        print(f"🔧 Initializing Firecrawl scraper with {timeout_seconds}s timeout and {cache_hours}h cache...")
        scraper = JobScraper(timeout_seconds=timeout_seconds, cache_hours=cache_hours)
        print("✅ Firecrawl scraper with intelligent caching initialized successfully!")
        return scraper
    except ValueError as e:
        print(f"❌ Failed to create scraper: {str(e)}")
        print("🔄 Will use mock scraping instead")
        logger.error(f"Failed to create scraper: {str(e)}")
        return None
